import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { object } from "zod";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}


export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    weekday: "long",
    day: "numeric",
    month: "long",
    year: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  };
  return date.toLocaleDateString("en-US", options);
};


export function extractErrorMessage(error: any) {

  console.log("error", error);

  if (error?.errors && typeof error?.errors === "string") {
    return error?.errors;
  }

  if (typeof error?.errors === "object" && error?.errors !== null) {
    const firstKey = Object.keys(error.errors)[0];
    const messages = error.errors[firstKey];
    console.log("firstKey", firstKey);
    if (Array.isArray(messages) && messages.length > 0) {
      return messages[0];
    }
    if (typeof messages === "string") {
      return messages;
    }
  }

  if (typeof error?.message === "object" && error?.message !== null) {
    const firstKey = Object.keys(error.message)[0];
    const messages = error.message[firstKey];
    if (Array.isArray(messages) && messages.length > 0) {
      return messages[0];
    }
    if (typeof messages === "string") {
      return messages;
    }
  }

  if (typeof error?.message === "string")
    return error?.message;

  return null;
}

export function modifyDate(date: string) {
  const d = new Date(date);
  if (isNaN(d.getTime())) {
    return "N/A";
  }
  const day = d.getDate();
  const month = d.toLocaleString("en-US", { month: "long" });
  const year = d.getFullYear();

  function getOrdinal(n: number) {
    if (n > 3 && n < 21) return "th";
    switch (n % 10) {
      case 1: return "st";
      case 2: return "nd";
      case 3: return "rd";
      default: return "th";
    }
  }

  return `${day}${getOrdinal(day)} ${month} ${year}`;
}