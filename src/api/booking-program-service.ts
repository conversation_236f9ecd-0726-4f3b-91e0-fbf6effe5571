import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import axiosInstance, { endpoints, epicFetcher } from "./axios";

export function useGetPublicProgramList({ page, limit }: { page: number; limit: number }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getAllPublicPrograms}?page=${page}&limit=${limit}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicProgramList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicProgramList: queueData,
            publicProgramListLoading: isLoading,
            publicProgramListError: error,
            publicProgramListValidating: isValidating,
            publicProgramListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicProgramList,
    };
}