"use client";
import { useGetPublicProgramList } from "@/api/booking-program-service";
import React from "react";

const BookingProgramSection = () => {
  const {
    publicProgramList,
    publicProgramListLoading,
    publicProgramListError,
    publicProgramListValidating,
    publicProgramListEmpty,
    revalidateGetPublicProgramList,
  } = useGetPublicProgramList({ page: 1, limit: 100 });

  console.log("publicProgramList", publicProgramList);

  return (
    <div className="container mx-auto px-4 py-1">
      <div className="inline-flex w-full flex-col items-center justify-start">
        <div className="font-helvetical justify-center self-stretch text-center text-[25px] leading-[25px] font-bold text-black">
          Choose Your Program
        </div>
        <div className="font-helvetical w-[626px] justify-center text-center text-[15px] leading-relaxed font-normal text-[#364153]">
          Each weekly session develops your technique, strategy, and consistency through game-like
          drills, with programs available in various cities to suit your goals and schedule.
        </div>
      </div>

      <div className="mt-4">
        {publicProgramListLoading ? (
          <div>Loading...</div>
        ) : publicProgramListError ? (
          <div>Error: {publicProgramListError.message}</div>
        ) : publicProgramListEmpty ? (
          <div>No programs available</div>
        ) : (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
            {publicProgramList.map((program: any) => (
              <div
                key={program.id}
                className="flex flex-col rounded-[10px] border border-[#c3c3c3] p-4"
              >
                <div className="flex flex-col">
                  <div className="font-helvetica text-[15px] font-bold text-black">
                    {program.name}
                  </div>
                  <div className="font-helvetica text-[13px] font-normal text-[#364153]">
                    {program.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BookingProgramSection;


