"use client";
import { useGetPublicProgramList } from "@/api/booking-program-service";
import React, { useState, useCallback } from "react";
import { ChevronLeft } from "lucide-react";
import ProgramCard from "./programs/program-card";

export interface Program {
  id: number;
  name: string;
  cover_image: string;
  description: string;
  category?: {
    name: string;
  };
  // Add other program properties as needed
}

const BookingProgramSection = () => {
  const [currentStep, setCurrentStep] = useState<"list" | "details">("list");
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);

  const {
    publicProgramList,
    publicProgramListLoading,
    publicProgramListError,
    publicProgramListEmpty,
  } = useGetPublicProgramList({ page: 1, limit: 100 });

  console.log("publicProgramList", publicProgramList);

  const handleBookNow = useCallback((program: Program) => {
    setSelectedProgram(program);
    setCurrentStep("details");
  }, []);

  const handleBackToList = useCallback(() => {
    setCurrentStep("list");
    setSelectedProgram(null);
  }, []);

  //   if (currentStep === "details" && selectedProgram) {
  //     return <ProgramDetailsStep program={selectedProgram} onBack={handleBackToList} />;
  //   }

  return (
    <div className="container mx-auto px-4 py-1">
      <div className="inline-flex w-full flex-col items-center justify-start">
        <div className="font-helvetical justify-center self-stretch text-center text-[25px] leading-[25px] font-bold text-black">
          Choose Your Program
        </div>
        <div className="font-helvetical w-[626px] justify-center text-center text-[15px] leading-relaxed font-normal text-[#364153]">
          Each weekly session develops your technique, strategy, and consistency through game-like
          drills, with programs available in various cities to suit your goals and schedule.
        </div>
      </div>

      {currentStep !== "details" || !selectedProgram ? (
        <div className="mt-4">
          {publicProgramListLoading ? (
            <div>Loading...</div>
          ) : publicProgramListError ? (
            <div>Error: {publicProgramListError.message}</div>
          ) : publicProgramListEmpty ? (
            <div>No programs available</div>
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
              {publicProgramList.map((program: Program) => (
                <ProgramCard key={program.id} program={program} onBookNow={handleBookNow} />
              ))}
            </div>
          )}
        </div>
      ) : (
        <ProgramDetailsStep program={selectedProgram} onBack={handleBackToList} />
      )}
    </div>
  );
};

export default BookingProgramSection;

interface ProgramDetailsStepProps {
  program: Program;
  onBack: () => void;
}

const ProgramDetailsStep = ({ program, onBack }: ProgramDetailsStepProps) => {
  return (
    <div className="container mx-auto px-4 py-1">
      {/* Header with back button */}
      <div className="mb-6 flex items-center">
        <button
          onClick={onBack}
          className="mr-4 flex items-center text-[#1c5534] transition-colors hover:text-[#164429]"
        >
          <ChevronLeft className="mr-1 h-8 w-6 stroke-3" />
        </button>
      </div>

      {/* Program Details */}
      <div className="mx-auto max-w-4xl">
        <div className="mb-6">
          <p className="font-helvetica mb-2 text-[14px] font-medium text-[#969696]">
            {program?.category?.name || "No category"}
          </p>
          <h1 className="font-helvetical mb-4 text-[32px] leading-[32px] font-bold text-black">
            {program.name}
          </h1>
          <p className="font-helvetical text-[16px] leading-relaxed font-normal text-[#364153]">
            {program.description}
          </p>
        </div>

        {/* Program Details Section */}
        <div className="mb-6 rounded-[20px] border border-[#e5e5e5] bg-white p-6">
          <h2 className="font-helvetica mb-4 text-[20px] font-bold text-black">Program Details</h2>

          {/* Placeholder for program details - you can expand this based on available data */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h3 className="font-helvetica mb-2 text-[16px] font-semibold text-black">
                What's Included
              </h3>
              <ul className="font-helvetica space-y-1 text-[14px] text-[#364153]">
                <li>• Professional coaching</li>
                <li>• Equipment provided</li>
                <li>• Small group sessions</li>
                <li>• Progress tracking</li>
              </ul>
            </div>

            <div>
              <h3 className="font-helvetica mb-2 text-[16px] font-semibold text-black">
                Schedule & Duration
              </h3>
              <div className="font-helvetica space-y-1 text-[14px] text-[#364153]">
                <p>Duration: 8 weeks</p>
                <p>Sessions: 2 per week</p>
                <p>Duration: 90 minutes each</p>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Section */}
        <div className="rounded-[20px] border border-[#ddba0a] bg-[#fffaed] p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h3 className="font-helvetica mb-2 text-[18px] font-bold text-black">
                Ready to Start?
              </h3>
              <p className="font-helvetica text-[14px] text-[#364153]">
                Book your spot in this program and begin your padel journey.
              </p>
            </div>
            <button className="font-helvetica rounded-[8px] bg-[#1c5534] px-8 py-3 font-bold whitespace-nowrap text-white transition-colors hover:bg-[#164429]">
              Proceed to Booking
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
