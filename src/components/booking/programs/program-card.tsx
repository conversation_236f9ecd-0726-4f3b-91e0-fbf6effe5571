import { Program } from "../booking-program-section";

interface ProgramCardProps {
  program: Program;
  onBookNow: (program: Program) => void;
}

const ProgramCard = ({ program, onBookNow }: ProgramCardProps) => {
  return (
    <div
      key={program.id}
      className="inline-flex w-[361px] flex-col items-start justify-start overflow-hidden rounded-xl bg-white pb-2.5 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]"
    >
      <img
        className="h-[125px] w-full object-cover object-top-left"
        src={program?.cover_image || "/imgs/placeholder.svg"}
      />
      <div className="flex flex-col items-start justify-start gap-2.5 self-stretch px-5 py-2.5">
        <div
          data-color="Success"
          data-icon="Dot"
          data-size="lg"
          data-type="Pill color"
          className="size- inline-flex items-center justify-start gap-1.5 rounded-2xl bg-[#fffaed] py-1 pr-3 pl-2.5 outline outline-1 outline-offset-[-1px] outline-[#ddba0a]"
        >
          <div data-svg-wrapper data-outline="False" data-size="sm" className="relative">
            <svg
              width="9"
              height="8"
              viewBox="0 0 9 8"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="4.5" cy="4" r="3" fill="#1C5534" />
            </svg>
          </div>
          <div className="font-helvetica justify-start text-center text-xs leading-[25px] font-normal text-[#1c5534]">
            {program?.category?.name || "No category"}
          </div>
        </div>
        <div className="size- flex flex-col items-start justify-start gap-[30px]">
          <div className="justify-start font-['Helvetica'] text-xl font-bold text-black">
            {program.name || "No name"}
          </div>
          <div className="size- flex flex-col items-start justify-start">
            <div className="size- inline-flex items-center justify-start">
              <div className="relative size-6 overflow-hidden">
                <div data-svg-wrapper className="absolute top-[3px] left-[3px]">
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15.7258 7.75117H2.67578M12.1008 1.95117V4.85117M6.30078 1.95117V4.85117M6.15578 16.4512H12.2458C13.4639 16.4512 14.073 16.4512 14.5382 16.2141C14.9475 16.0056 15.2802 15.6729 15.4887 15.2636C15.7258 14.7983 15.7258 14.1893 15.7258 12.9712V6.88117C15.7258 5.66306 15.7258 5.054 15.4887 4.58874C15.2802 4.17949 14.9475 3.84676 14.5382 3.63823C14.073 3.40117 13.4639 3.40117 12.2458 3.40117H6.15578C4.93767 3.40117 4.32861 3.40117 3.86335 3.63823C3.4541 3.84676 3.12137 4.17949 2.91284 4.58874C2.67578 5.054 2.67578 5.66306 2.67578 6.88117V12.9712C2.67578 14.1893 2.67578 14.7983 2.91284 15.2636C3.12137 15.6729 3.4541 16.0056 3.86335 16.2141C4.32861 16.4512 4.93767 16.4512 6.15578 16.4512Z"
                      stroke="#1C5534"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
              <div className="justify-start font-['Helvetica'] text-sm leading-[25px] font-normal text-black">
                Apr 21st - Jun 9th (7 days)
              </div>
            </div>
            <div className="size- inline-flex items-center justify-start">
              <div className="relative size-6 overflow-hidden">
                <div data-svg-wrapper className="absolute top-[3px] left-[3px]">
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15.7258 8.83867V6.88117C15.7258 5.66306 15.7258 5.054 15.4887 4.58874C15.2802 4.17949 14.9475 3.84676 14.5382 3.63823C14.073 3.40117 13.4639 3.40117 12.2458 3.40117H6.15578C4.93767 3.40117 4.32861 3.40117 3.86335 3.63823C3.4541 3.84676 3.12137 4.17949 2.91284 4.58874C2.67578 5.054 2.67578 5.66306 2.67578 6.88117V12.9712C2.67578 14.1893 2.67578 14.7983 2.91284 15.2636C3.12137 15.6729 3.4541 16.0056 3.86335 16.2141C4.32861 16.4512 4.93767 16.4512 6.15578 16.4512H9.56328M15.7258 7.75117H2.67578M12.1008 1.95117V4.85117M6.30078 1.95117V4.85117M13.5508 15.7262V11.3762M11.3758 13.5512H15.7258"
                      stroke="#1C5534"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
              <div className="justify-start font-['Helvetica'] text-sm leading-[25px] font-normal text-black">
                4/21- 4/28 - 5/5 - 5/12 - 5/19 - 5/26 - 6/3
              </div>
            </div>
            <div className="size- inline-flex items-center justify-start">
              <div className="relative size-6 overflow-hidden">
                <div data-svg-wrapper className="absolute top-[3px] left-[3px]">
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.19922 4.8502V9.2002L12.0992 10.6502M16.4492 9.2002C16.4492 13.2043 13.2033 16.4502 9.19922 16.4502C5.19516 16.4502 1.94922 13.2043 1.94922 9.2002C1.94922 5.19613 5.19516 1.9502 9.19922 1.9502C13.2033 1.9502 16.4492 5.19613 16.4492 9.2002Z"
                      stroke="#1C5534"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
              <div className="justify-start font-['Helvetica'] text-sm leading-[25px] font-normal text-black">
                5:00 pm - 7:00 pm
              </div>
            </div>
            <div className="size- inline-flex items-center justify-start">
              <div className="relative size-6 overflow-hidden">
                <div data-svg-wrapper className="absolute top-[3px] left-[3px]">
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M16.4492 15.2248V13.7748C16.4492 12.4235 15.525 11.2881 14.2742 10.9662M11.7367 2.3856C12.7995 2.81581 13.5492 3.85776 13.5492 5.07481C13.5492 6.29186 12.7995 7.3338 11.7367 7.76401M12.8242 15.2248C12.8242 13.8736 12.8242 13.198 12.6035 12.665C12.3091 11.9544 11.7446 11.3899 11.034 11.0956C10.5011 10.8748 9.82545 10.8748 8.47422 10.8748H6.29922C4.94799 10.8748 4.27237 10.8748 3.73944 11.0956C3.02886 11.3899 2.4643 11.9544 2.16997 12.665C1.94922 13.198 1.94922 13.8736 1.94922 15.2248M10.2867 5.07481C10.2867 6.67643 8.98835 7.97481 7.38672 7.97481C5.78509 7.97481 4.48672 6.67643 4.48672 5.07481C4.48672 3.47318 5.78509 2.1748 7.38672 2.1748C8.98835 2.1748 10.2867 3.47318 10.2867 5.07481Z"
                      stroke="#1C5534"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
              <div className="justify-center font-['Helvetica'] text-sm font-normal text-black">
                2 of 12 spots Remaining
              </div>
            </div>
          </div>
          <div className="size- inline-flex items-center justify-center gap-2.5 rounded-[10px] px-[5px] py-0.5 outline outline-1 outline-offset-[-1px] outline-[#c3c3c3]">
            <div className="justify-start font-['Helvetica'] text-[25px] font-bold text-[#1c5534]">
              $595
            </div>
          </div>
        </div>
        <button className="text-primary w-full rounded-full bg-[#ddba0a] px-10 py-2.5 text-center font-['Helvetica'] text-base leading-none font-bold shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]">
          Enroll Now
        </button>
      </div>
    </div>
  );
};

export default ProgramCard;
