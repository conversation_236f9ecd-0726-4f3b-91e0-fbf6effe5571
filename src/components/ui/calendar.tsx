"use client";

import * as React from "react";
import { ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import { DayButton, DayPicker, getDefaultClassNames } from "react-day-picker";

import { cn } from "@/libs/utils";
import { Button } from "@/components/ui/button";

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  captionLayout = "label",
  buttonVariant = "ghost",
  formatters,
  components,
  ...props
}: React.ComponentProps<typeof DayPicker> & {
  buttonVariant?: React.ComponentProps<typeof Button>["variant"];
}) {
  const defaultClassNames = getDefaultClassNames();

  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn(
        "group/calendar font-helvetica bg-white p-4 [--cell-size:--spacing(10)] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent",
        "rounded-xl border border-gray-200 shadow-sm",
        String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,
        String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,
        className
      )}
      captionLayout={captionLayout}
      formatters={{
        formatMonthDropdown: (date) => date.toLocaleString("default", { month: "short" }),
        ...formatters,
      }}
      classNames={{
        root: cn("w-fit", defaultClassNames.root),
        months: cn("flex gap-6 flex-col md:flex-row relative", defaultClassNames.months),
        month: cn("flex flex-col w-full gap-5", defaultClassNames.month),
        nav: cn(
          "flex items-center gap-2 w-full absolute top-0 inset-x-0 justify-between px-1",
          defaultClassNames.nav
        ),
        button_previous: cn(
          "h-9 w-9 bg-white border border-gray-200 hover:bg-primary hover:text-white hover:border-primary flex items-center justify-center transition-all duration-200 rounded-full p-0 select-none shadow-sm",
          "aria-disabled:opacity-50 aria-disabled:cursor-not-allowed",
          defaultClassNames.button_previous
        ),
        button_next: cn(
          "h-9 w-9 bg-white border border-gray-200 text-center flex items-center justify-center hover:bg-primary hover:text-white hover:border-primary transition-all duration-200 rounded-full p-0 select-none shadow-sm",
          "aria-disabled:opacity-50 aria-disabled:cursor-not-allowed",
          defaultClassNames.button_next
        ),
        month_caption: cn(
          "flex items-center justify-center h-12 w-full px-4 mb-2",
          defaultClassNames.month_caption
        ),
        dropdowns: cn(
          "w-full flex items-center text-base font-medium justify-center h-12 gap-2 font-helvetica",
          defaultClassNames.dropdowns
        ),
        dropdown_root: cn(
          "relative has-focus:border-primary border border-gray-200 shadow-sm has-focus:ring-primary/20 has-focus:ring-2 rounded-lg bg-white",
          defaultClassNames.dropdown_root
        ),
        dropdown: cn("absolute bg-white inset-0 opacity-0 rounded-lg", defaultClassNames.dropdown),
        caption_label: cn(
          "select-none font-medium text-foreground font-helvetica",
          captionLayout === "label"
            ? "text-lg font-semibold"
            : "rounded-lg pl-3 pr-2 flex items-center gap-2 text-base h-10 bg-white border border-gray-200 hover:border-primary transition-colors [&>svg]:text-gray-500 [&>svg]:size-4",
          defaultClassNames.caption_label
        ),
        table: "w-full border-collapse",
        weekdays: cn("flex mb-2", defaultClassNames.weekdays),
        weekday: cn(
          "text-gray-600 rounded-md flex-1 font-medium text-sm select-none py-2 text-center font-helvetica",
          defaultClassNames.weekday
        ),
        week: cn("flex w-full mt-1", defaultClassNames.week),
        week_number_header: cn(
          "select-none w-10 text-center",
          defaultClassNames.week_number_header
        ),
        week_number: cn(
          "text-sm select-none text-gray-500 font-helvetica",
          defaultClassNames.week_number
        ),
        day: cn(
          "relative w-full h-full p-0.5 text-center [&:first-child[data-selected=true]_button]:rounded-l-lg [&:last-child[data-selected=true]_button]:rounded-r-lg group/day aspect-square select-none",
          defaultClassNames.day
        ),
        range_start: cn("rounded-l-lg bg-primary/10", defaultClassNames.range_start),
        range_middle: cn("rounded-none bg-primary/5", defaultClassNames.range_middle),
        range_end: cn("rounded-r-lg bg-primary/10", defaultClassNames.range_end),
        today: cn(
          "bg-secondary/20 text-foreground rounded-lg font-semibold data-[selected=true]:bg-primary data-[selected=true]:text-white",
          defaultClassNames.today
        ),
        outside: cn(
          "text-gray-400 aria-selected:text-gray-400 opacity-50",
          defaultClassNames.outside
        ),
        disabled: cn("text-gray-300 opacity-30 cursor-not-allowed", defaultClassNames.disabled),
        hidden: cn("invisible", defaultClassNames.hidden),
        ...classNames,
      }}
      components={{
        Root: ({ className, rootRef, ...props }) => {
          return <div data-slot="calendar" ref={rootRef} className={cn(className)} {...props} />;
        },
        Chevron: ({ className, orientation, ...props }) => {
          if (orientation === "left") {
            return <ChevronLeftIcon className={cn("size-4", className)} {...props} />;
          }

          if (orientation === "right") {
            return <ChevronRightIcon className={cn("size-4", className)} {...props} />;
          }

          return <ChevronDownIcon className={cn("size-4", className)} {...props} />;
        },
        DayButton: CalendarDayButton,
        WeekNumber: ({ children, ...props }) => {
          return (
            <td {...props}>
              <div className="font-helvetica flex h-10 w-10 items-center justify-center text-center text-sm font-medium text-gray-500">
                {children}
              </div>
            </td>
          );
        },
        ...components,
      }}
      {...props}
    />
  );
}

function CalendarDayButton({
  className,
  day,
  modifiers,
  ...props
}: React.ComponentProps<typeof DayButton>) {
  const defaultClassNames = getDefaultClassNames();

  const ref = React.useRef<HTMLButtonElement>(null);
  React.useEffect(() => {
    if (modifiers.focused) ref.current?.focus();
  }, [modifiers.focused]);

  return (
    <Button
      ref={ref}
      variant="ghost"
      size="icon"
      data-day={day.date.toLocaleDateString()}
      data-selected-single={
        modifiers.selected &&
        !modifiers.range_start &&
        !modifiers.range_end &&
        !modifiers.range_middle
      }
      data-range-start={modifiers.range_start}
      data-range-end={modifiers.range_end}
      data-range-middle={modifiers.range_middle}
      className={cn(
        "font-helvetica flex h-10 w-10 items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 aria-selected:opacity-100",
        "hover:bg-primary/10 hover:text-primary hover:scale-105 hover:shadow-sm",
        "focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-primary/20 focus-visible:ring-2",
        "data-[selected-single=true]:bg-primary data-[selected-single=true]:font-semibold data-[selected-single=true]:text-white data-[selected-single=true]:shadow-md",
        "data-[selected-single=true]:hover:bg-primary/90 data-[selected-single=true]:hover:scale-105",
        "data-[range-start=true]:bg-primary data-[range-start=true]:rounded-l-lg data-[range-start=true]:font-semibold data-[range-start=true]:text-white data-[range-start=true]:shadow-md",
        "data-[range-end=true]:bg-primary data-[range-end=true]:rounded-r-lg data-[range-end=true]:font-semibold data-[range-end=true]:text-white data-[range-end=true]:shadow-md",
        "data-[range-middle=true]:bg-primary/10 data-[range-middle=true]:text-primary data-[range-middle=true]:rounded-none",
        "group-data-[focused=true]/day:ring-primary/30 group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-2",
        defaultClassNames.day,
        className
      )}
      {...props}
    />
  );
}

export { Calendar, CalendarDayButton };
